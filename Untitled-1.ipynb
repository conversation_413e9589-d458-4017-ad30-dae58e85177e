{"cells": [{"cell_type": "code", "execution_count": 3, "id": "72e717e4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["the factorial of 5 is : 120\n"]}], "source": ["from math import factorial \n", "print(\"the factorial of 5 is :\",factorial(5))"]}, {"cell_type": "code", "execution_count": 4, "id": "3c1de9ce", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["the factorial of  9  is : 362880\n"]}], "source": ["value=input(\"enter the value for the intended factotrial:\")\n", "print(\"the factorial of \",value,\" is :\",factorial(int(value)))"]}, {"cell_type": "code", "execution_count": 6, "id": "843e0953", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["the factorial of  9  is : 362880\n"]}], "source": ["value=input(\"enter the value for the intended factotrial:\")\n", "if value==0:\n", "    print(\"the factorial of 0 is 1\")\n", "else:\n", "    print(\"the factorial of \",value,\" is :\",factorial(int(value)))"]}, {"cell_type": "code", "execution_count": 1, "id": "c1aafec7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON>\n", "<PERSON><PERSON>\n", "Zakaryia\n", "<PERSON><PERSON><PERSON>\n"]}], "source": ["team=[\"<PERSON><PERSON>\",\"<PERSON><PERSON>\",\"<PERSON><PERSON><PERSON><PERSON>\",\"<PERSON><PERSON><PERSON>\"]\n", "for name in team:\n", "    print (name)"]}, {"cell_type": "code", "execution_count": 3, "id": "dc998d5e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n", "2\n", "4\n", "6\n", "8\n", "10\n"]}], "source": ["for num in range(0,11,2):\n", "    print(num)"]}, {"cell_type": "code", "execution_count": 6, "id": "26798629", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MOHANAD\n", "mohanad\n"]}], "source": ["import string\n", "Name=\"<PERSON><PERSON>\"\n", "print(Name.upper())\n", "print(Name.lower())\n"]}, {"cell_type": "code", "execution_count": 8, "id": "c3ff7fe5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["mohanad ezz\n", "<PERSON><PERSON>\n", "<PERSON><PERSON>\n"]}], "source": ["fname=\"mohanad\"\n", "lname=\"ezz\"\n", "FullName=(fname+\" \"+lname).title()\n", "print(fname+\" \"+lname)\n", "print(FullName.title())\n", "print(FullName)"]}, {"cell_type": "code", "execution_count": 3, "id": "cb72317a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   1 \n", "  2 3 \n", " 4 5 6 \n", "7 8 9 10 \n"]}], "source": ["    rows=4\n", "    num = 1\n", "    for i in range(1, rows + 1):\n", "        # Print spaces for alignment (optional)\n", "        print(\" \" * (rows - i), end=\"\")\n", "        \n", "        # Print numbers for current row\n", "        for j in range(i):\n", "            print(num, end=\" \")\n", "            num += 1\n", "        \n", "        # Move to next line\n", "        print()\n"]}, {"cell_type": "code", "execution_count": null, "id": "538e2d02", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.7"}}, "nbformat": 4, "nbformat_minor": 5}